<template>
  <transition name="dialog-fade" appear>
    <view
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center"
      @touchmove.prevent
    >
      <view class="absolute inset-0 bg-black bg-opacity-50" @click="handleCancel"></view>
      <view
        class="dialog-container relative flex flex-col w-90% max-w-[600rpx] overflow-hidden bg-white rounded-[12rpx] transition-all duration-300 transform"
      >
        <view
          class="flex items-center justify-between p-[24rpx] border-b border-b-solid border-[#eee]"
        >
          <slot name="title">
            <text class="text-[32rpx] font-600 text-[#333]">{{ title }}</text>
          </slot>
          <wd-icon name="close" size="20px" class="text-[#999]" @click="handleCancel" />
        </view>
        <view class="p-[32rpx_24rpx] text-center">
          <slot>
            <slot name="content">
              <text class="text-[28rpx] leading-normal text-[#666]">{{ message }}</text>
            </slot>
          </slot>
        </view>
        <view
          class="flex gap-[20rpx] justify-end p-[20rpx_24rpx] border-t border-t-solid border-[#eee]"
        >
          <ActionButton type="secondary" :text="cancelText" @click="handleCancel" />
          <ActionButton type="primary" :text="confirmText" @click="handleConfirm" />
        </view>
      </view>
    </view>
  </transition>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import ActionButton from './ActionButton.vue'

// Props定义
interface Props {
  /** 对话框标题 */
  title?: string
  /** 对话框消息内容 */
  message?: string
  /** 确认按钮文本 */
  confirmText?: string
  /** 取消按钮文本 */
  cancelText?: string
}

// 事件定义
interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '提示',
  message: '',
  confirmText: '确认',
  cancelText: '取消',
})

const emit = defineEmits<Emits>()

// 控制显示状态
const visible = ref(false)

// 显示对话框
const show = () => {
  visible.value = true
}

// 隐藏对话框
const hide = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  hide()
  emit('confirm')
}

// 处理取消
const handleCancel = () => {
  hide()
  emit('cancel')
}

// 对外暴露方法
defineExpose({
  show,
  hide,
})
</script>

<style lang="scss" scoped>
// 组件样式已通过UnoCSS类实现，无需额外样式

// 添加过渡动画样式
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
}

.dialog-fade-enter-active .dialog-container,
.dialog-fade-leave-active .dialog-container {
  transition: all 0.3s ease;
}

.dialog-fade-enter-from .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}

.dialog-fade-leave-to .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}
</style>
