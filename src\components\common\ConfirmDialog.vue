<template>
  <transition name="dialog-fade" appear>
    <view v-if="visible" class="course-confirm-dialog" @touchmove.prevent>
      <view class="dialog-mask" @click="handleCancel"></view>
      <view class="dialog-container">
        <view class="dialog-header">
          <slot name="title">
            <text class="dialog-title">{{ title }}</text>
          </slot>
          <wd-icon name="close" size="20px" class="close-icon" @click="handleCancel" />
        </view>
        <view class="dialog-content">
          <slot>
            <slot name="content">
              <text class="dialog-message">{{ message }}</text>
            </slot>
          </slot>
        </view>
        <view class="dialog-footer">
          <ActionButton type="secondary" :text="cancelText" @click="handleCancel" />
          <ActionButton type="primary" :text="confirmText" @click="handleConfirm" />
        </view>
      </view>
    </view>
  </transition>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import ActionButton from './ActionButton.vue'

// Props定义
interface Props {
  /** 对话框标题 */
  title?: string
  /** 对话框消息内容 */
  message?: string
  /** 确认按钮文本 */
  confirmText?: string
  /** 取消按钮文本 */
  cancelText?: string
}

// 事件定义
interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '提示',
  message: '',
  confirmText: '确认',
  cancelText: '取消',
})

const emit = defineEmits<Emits>()

// 控制显示状态
const visible = ref(false)

// 显示对话框
const show = () => {
  visible.value = true
}

// 隐藏对话框
const hide = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  hide()
  emit('confirm')
}

// 处理取消
const handleCancel = () => {
  hide()
  emit('cancel')
}

// 对外暴露方法
defineExpose({
  show,
  hide,
})
</script>

<style lang="scss" scoped>
// 自定义确认对话框样式 - 与college-courses.vue保持一致
.course-confirm-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  color: #999;
}

.dialog-content {
  flex: 1;
  padding: 32rpx 24rpx;
  overflow-y: auto;
  text-align: center;
}

.dialog-message {
  font-size: 28rpx;
  line-height: 1.5;
  color: #666;
}

.dialog-footer {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
  padding: 20rpx 24rpx;
  border-top: 1px solid #eee;
}

// 添加过渡动画样式
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
}

.dialog-fade-enter-active .dialog-container,
.dialog-fade-leave-active .dialog-container {
  transition: all 0.3s ease;
}

.dialog-fade-enter-from .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}

.dialog-fade-leave-to .dialog-container {
  opacity: 0;
  transform: scale(0.95);
}
</style>
