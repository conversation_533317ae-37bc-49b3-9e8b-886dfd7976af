<route lang="json5">
{
  style: {
    navigationBarTitleText: '授课进度制定',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { getTeachingScheduleArrangementList } from '@/service/teachingTask'
import { saveTeachingProcess, submitTeachingProcess } from '@/service/teacher'
import type {
  TeachingScheduleArrangementListResponse,
  TeachingScheduleItem,
  TeacherOption,
  TeachingMethodOption,
  SiteTypeOption,
  StudentGroupOption,
} from '@/types/teachingTask'
import type { SaveTeachingProcessRequest, TeachingProcessItem } from '@/types/teacher'
import { useTeachingTaskStore } from '@/store/teachingTask'
import FileUploader from '@/components/common/FileUploader.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import ConfirmDialog from '@/components/common/ConfirmDialog.vue'
import { useToast } from 'wot-design-uni'
import { debounce } from '@/utils/index'

// 定义扩展的类型，包含附件相关属性
interface TeachingScheduleItemExtended extends TeachingScheduleItem {
  lessonAttachments?: Array<{ url: string; name: string }>
  homeworkAttachments?: Array<{ url: string; name: string }>
  skcdlxshow?: string
}

// 获取教学任务store
const teachingTaskStore = useTeachingTaskStore()
// 添加toast提示
const toast = useToast()

// 课程基础信息
const courseInfo = reactive({
  className: '',
  courseName: '',
  coursePeriod: '',
  totalHours: '',
  weeklyHours: '',
  currentWeek: 0,
  representative: {
    id: '',
    name: '',
  },
})

// 授课任务数据
const taskData = ref<TeachingScheduleArrangementListResponse | null>(null)

// 授课进度列表
const progressList = ref<TeachingScheduleItemExtended[]>([])

// 当前编辑的进度项索引
const currentEditIndex = ref(-1)

// 教师选项列表
const teacherOptions = ref<TeacherOption[]>([])

// 授课方式选项列表
const teachingMethodOptions = ref<TeachingMethodOption[]>([])

// 教室要求选项列表
const siteTypeOptions = ref<SiteTypeOption[]>([])

// 学生分组选项列表
const studentGroupOptions = ref<StudentGroupOption[]>([])

// 作业批改方式选项列表
const homeworkCorrectionOptions = ref<{ value: string; label: string }[]>([])

// 添加保存和提交按钮的加载状态
const isSaving = ref(false)
const isSubmitting = ref(false)

// 确认对话框相关状态
const confirmDialogRef = ref()
const currentClearItem = ref<TeachingScheduleItemExtended | null>(null)

// 处理各项目的附件数据 - 将字符串转为数组
const getAttachmentsFromString = (attachmentStr: string | undefined) => {
  if (!attachmentStr) return []

  return attachmentStr.split(',').map((item) => {
    // 检查是否包含分隔符"|"
    if (item.includes('|')) {
      const [url, fileName] = item.split('|')
      return {
        url,
        name: fileName,
      }
    } else {
      // 如果没有分隔符，假设整个字符串是文件名
      return {
        url: '',
        name: item,
      }
    }
  })
}

// 将附件数组转换为字符串
const getStringFromAttachments = (attachments: Array<{ url: string; name: string }>) => {
  if (!attachments || attachments.length === 0) return ''

  return attachments.map((file) => `${file.url}|${file.name}`).join(',')
}

// 获取星期几名称
const getDayOfWeek = (day: number): string => {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return days[day] || ''
}

// 格式化显示星期几
const formatWeekDay = (day: number): string => {
  return getDayOfWeek(day)
}

// 获取授课任务数据
const fetchTaskData = async () => {
  // 从store中获取教学任务ID
  const jxrwid = teachingTaskStore.currentTask.id
  if (!jxrwid) {
    console.error('未获取到教学任务ID')
    return
  }

  const res = await getTeachingScheduleArrangementList({ jxrwid: jxrwid.toString() })
  taskData.value = res

  // 使用解构赋值更新课程基础信息
  const { bjmc, kcmc, ksz, jsz, xn, xq, xqzxs, zxs, kdb, kdbxm } = res.jxrw

  courseInfo.className = bjmc
  courseInfo.courseName = kcmc
  courseInfo.coursePeriod = `[${ksz} - ${jsz}周] ${xn}(${xq})`
  courseInfo.totalHours = xqzxs
  courseInfo.weeklyHours = zxs
  courseInfo.currentWeek = jsz - ksz + 1
  courseInfo.representative.id = kdb || ''
  courseInfo.representative.name = kdbxm || ''

  // 更新授课进度列表数据
  if (res.skjhList && res.skjhList.length > 0) {
    progressList.value = res.skjhList
  } else if (res.list && res.list.length > 0) {
    progressList.value = res.list
  }

  // 提取教师选项列表
  if (res.skjs && Array.isArray(res.skjs)) {
    teacherOptions.value = res.skjs
  }

  // 提取授课方式选项列表
  if (res.skfs && Array.isArray(res.skfs)) {
    teachingMethodOptions.value = res.skfs
  }

  // 提取教室要求选项列表
  if (res.cdlx && Array.isArray(res.cdlx)) {
    siteTypeOptions.value = res.cdlx
  }

  // 提取学生分组选项列表
  if (res.xsfz && Array.isArray(res.xsfz)) {
    studentGroupOptions.value = res.xsfz
  }

  // 提取作业批改方式选项列表
  if (res.zydm && Array.isArray(res.zydm)) {
    homeworkCorrectionOptions.value = res.zydm
  }

  // 为每个条目初始化附件数组和关联选项名称
  progressList.value.forEach((item) => {
    // 使用Vue的响应式API来添加这些属性
    if (!item.lessonAttachments) {
      item.lessonAttachments = getAttachmentsFromString(item.fjlb)
    }
    if (!item.homeworkAttachments) {
      item.homeworkAttachments = getAttachmentsFromString(item.zyfjlb)
    }

    // 初始化教师名称
    if (item.skjs !== undefined && !item.skjsxm && teacherOptions.value.length > 0) {
      const selectedTeacher = teacherOptions.value.find((teacher) => teacher.value === item.skjs)
      if (selectedTeacher) {
        item.skjsxm = selectedTeacher.label
      }
    }

    // 初始化授课方式名称
    if (item.skfs !== undefined && !item.skfsmc && teachingMethodOptions.value.length > 0) {
      const selectedMethod = teachingMethodOptions.value.find(
        (method) => method.value === item.skfs,
      )
      if (selectedMethod) {
        item.skfsmc = selectedMethod.label
      }
    }

    // 初始化教室要求名称
    if (item.skcdlx !== undefined && siteTypeOptions.value.length > 0) {
      const selectedSiteType = siteTypeOptions.value.find(
        (siteType) => siteType.value === item.skcdlx,
      )
      if (selectedSiteType) {
        item.skcdlxshow = selectedSiteType.label
      }
    }

    // 初始化学生分组名称

    if (item.xsfzid !== undefined && !item.xsfzmc && studentGroupOptions.value.length > 0) {
      const selectedGroup = studentGroupOptions.value.find((group) => group.value === item.xsfzid)

      if (selectedGroup) {
        item.xsfzmc = selectedGroup.label
      }
    }
  })
}

// 是否有数据
const hasData = computed(() => progressList.value.length > 0)

// 获取教师姓名列表（用于picker显示）
const teacherNameList = computed(() => {
  return teacherOptions.value.map((option) => option.label)
})

// 获取授课方式名称列表（用于picker显示）
const teachingMethodNameList = computed(() => {
  return teachingMethodOptions.value.map((option) => option.label)
})

// 获取教室要求名称列表（用于picker显示）
const siteTypeNameList = computed(() => {
  return siteTypeOptions.value.map((option) => option.label)
})

// 获取学生分组名称列表（用于picker显示）
const studentGroupNameList = computed(() => {
  return studentGroupOptions.value.map((option) => option.label)
})

// 获取作业批改方式名称列表（用于picker显示）
const homeworkCorrectionNameList = computed(() => {
  return homeworkCorrectionOptions.value.map((option) => option.label)
})

// 获取作业批改方式的当前索引
const getHomeworkCorrectionIndex = (item: TeachingScheduleItemExtended): number => {
  const correctionCode = item.zypgfs
  return homeworkCorrectionOptions.value.findIndex(
    (correction) => correction.value === correctionCode,
  )
}

// 页面加载时获取数据
onMounted(() => {
  fetchTaskData()
})

// 添加按钮的点击事件
const handleAddProgress = () => {
  // 重置当前附件数据
  currentEditIndex.value = -1

  // 实现添加新的授课进度逻辑
  console.log('添加新的授课进度')
  // 这里可以添加跳转到编辑页面或打开编辑弹窗的逻辑
}

// 编辑按钮的点击事件
const handleEditProgress = (index: number) => {
  currentEditIndex.value = index
  const item = progressList.value[index]

  console.log('编辑授课进度', item)
  // 这里可以添加跳转到编辑页面或打开编辑弹窗的逻辑
}

// 取消编辑
const handleCancelEdit = () => {
  currentEditIndex.value = -1
}

// 保存编辑结果
const handleSaveEdit = () => {
  if (currentEditIndex.value >= 0) {
    // 将编辑结果保存到当前项
    const currentItem = progressList.value[currentEditIndex.value]
    currentItem.fjlb = getStringFromAttachments(currentItem.lessonAttachments)
    currentItem.zyfjlb = getStringFromAttachments(currentItem.homeworkAttachments)

    // TODO: 这里可以添加保存到后端的逻辑

    // 退出编辑模式
    currentEditIndex.value = -1
  }
}

// 处理教师选择变化
const handleTeacherChange = (e: any, item: TeachingScheduleItemExtended) => {
  const index = e.detail.value
  if (index >= 0 && index < teacherOptions.value.length) {
    const selectedTeacher = teacherOptions.value[index]
    // 更新当前项的授课教师
    item.skjs = selectedTeacher.value
    item.skjsxm = selectedTeacher.label
  }
}

// 处理授课方式选择变化
const handleTeachingMethodChange = (e: any, item: TeachingScheduleItemExtended) => {
  const index = e.detail.value
  if (index >= 0 && index < teachingMethodOptions.value.length) {
    const selectedMethod = teachingMethodOptions.value[index]
    // 更新当前项的授课方式
    item.skfs = selectedMethod.value
    item.skfsmc = selectedMethod.label
  }
}

// 处理教室要求选择变化
const handleSiteTypeChange = (e: any, item: TeachingScheduleItemExtended) => {
  const index = e.detail.value
  if (index >= 0 && index < siteTypeOptions.value.length) {
    const selectedSiteType = siteTypeOptions.value[index]
    // 更新当前项的教室要求
    item.skcdlx = selectedSiteType.value
    item.skcdlxshow = selectedSiteType.label
  }
}

// 处理学生分组选择变化
const handleStudentGroupChange = (e: any, item: TeachingScheduleItemExtended) => {
  const index = e.detail.value
  if (index >= 0 && index < studentGroupOptions.value.length) {
    const selectedGroup = studentGroupOptions.value[index]
    // 更新当前项的学生分组
    item.xsfzid = selectedGroup.value
    item.xsfzmc = selectedGroup.label
  }
}

// 获取教师的当前索引
const getTeacherIndex = (item: TeachingScheduleItemExtended): number => {
  const teacherCode = item.skjs
  return teacherOptions.value.findIndex((teacher) => teacher.value === teacherCode)
}

// 获取授课方式的当前索引
const getTeachingMethodIndex = (item: TeachingScheduleItemExtended): number => {
  const methodCode = item.skfs
  return teachingMethodOptions.value.findIndex((method) => method.value === methodCode)
}

// 获取教室要求的当前索引
const getSiteTypeIndex = (item: TeachingScheduleItemExtended): number => {
  const siteTypeCode = item.skcdlx
  return siteTypeOptions.value.findIndex((siteType) => siteType.value === siteTypeCode)
}

// 获取学生分组的当前索引
const getStudentGroupIndex = (item: TeachingScheduleItemExtended): number => {
  const groupId = item.xsfzid
  return studentGroupOptions.value.findIndex((group) => group.value === groupId)
}

// 处理教案附件变化
const handleLessonAttachmentsChange = (
  files: Array<{ url: string; name: string }>,
  item: TeachingScheduleItemExtended,
) => {
  // 更新该项目的教案附件
  item.lessonAttachments = files
  item.fjlb = getStringFromAttachments(files)
}

// 处理作业附件变化
const handleHomeworkAttachmentsChange = (
  files: Array<{ url: string; name: string }>,
  item: TeachingScheduleItemExtended,
) => {
  // 更新该项目的作业附件
  item.homeworkAttachments = files
  item.zyfjlb = getStringFromAttachments(files)
}

// 处理作业批改方式选择变化
const handleHomeworkCorrectionChange = (e: any, item: TeachingScheduleItemExtended) => {
  const index = e.detail.value
  if (index >= 0 && index < homeworkCorrectionOptions.value.length) {
    const selectedCorrection = homeworkCorrectionOptions.value[index]
    // 更新当前项的作业批改方式
    item.zypgfs = selectedCorrection.value
    item.zypgfsmc = selectedCorrection.label
  }
}

// 添加全局保存按钮的逻辑
const handleSaveAllProgress = async () => {
  // 如果正在保存，则不执行
  if (isSaving.value) return

  // 设置保存状态为true
  isSaving.value = true

  // 实现保存全部进度的逻辑
  console.log('保存全部进度', progressList.value)

  // 将所有项目的附件数据同步到fjlb和zyfjlb字段
  progressList.value.forEach((item) => {
    if (item.lessonAttachments) {
      item.fjlb = getStringFromAttachments(item.lessonAttachments)
    }
    if (item.homeworkAttachments) {
      item.zyfjlb = getStringFromAttachments(item.homeworkAttachments)
    }
  })

  // 获取教学任务ID
  const jxrwid = teachingTaskStore.currentTask.id
  if (!jxrwid) {
    toast.show({ msg: '未获取到教学任务ID', iconName: 'error' })
    isSaving.value = false
    return
  }

  try {
    // 构建提交数据
    const request: SaveTeachingProcessRequest = {
      jxrwid: jxrwid.toString(),
      data: progressList.value as unknown as TeachingProcessItem[],
    }

    // 调用API保存数据
    await saveTeachingProcess(request)

    // 保存成功提示
    toast.show({ msg: '保存成功', iconName: 'success' })

    fetchTaskData()
  } catch (error) {
    console.error('保存授课进度失败', error)
  } finally {
    // 无论成功或失败，都将保存状态设为false
    isSaving.value = false
  }
}

// 使用防抖包装保存函数，防止频繁点击
const debouncedSaveAllProgress = debounce(handleSaveAllProgress, 500)

// 提交教学进度按钮的逻辑
const handleSubmitAllProgress = async () => {
  // 如果正在提交，则不执行
  if (isSubmitting.value) return

  // 设置提交状态为true
  isSubmitting.value = true

  // 实现提交全部进度的逻辑
  console.log('提交全部进度', progressList.value)

  // 将所有项目的附件数据同步到fjlb和zyfjlb字段
  progressList.value.forEach((item) => {
    if (item.lessonAttachments) {
      item.fjlb = getStringFromAttachments(item.lessonAttachments)
    }
    if (item.homeworkAttachments) {
      item.zyfjlb = getStringFromAttachments(item.homeworkAttachments)
    }
  })

  // 获取教学任务ID
  const jxrwid = teachingTaskStore.currentTask.id
  if (!jxrwid) {
    toast.show({ msg: '未获取到教学任务ID', iconName: 'error' })
    isSubmitting.value = false
    return
  }

  try {
    // 构建提交数据
    const request: SaveTeachingProcessRequest = {
      jxrwid: jxrwid.toString(),
      data: progressList.value as unknown as TeachingProcessItem[],
    }

    // 调用API提交数据
    const result = await submitTeachingProcess(request)

    if (result) {
      // 显示成功对话框
      uni.showModal({
        title: '提示',
        content: '授课计划提交成功，点击确定按钮进入授课计划表配置',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定
            console.log('用户点击确定，准备进入授课计划表配置')
            uni.redirectTo({
              url: '/pages/teacher/teaching-affairs/task-management/teaching-plan/index',
            })
          }
        },
      })
    } else {
      toast.show({ msg: '提交失败', iconName: 'error' })
    }

    fetchTaskData()
  } catch (error) {
    console.error('提交授课进度失败', error)
    toast.show({ msg: '提交失败', iconName: 'error' })
  } finally {
    // 无论成功或失败，都将提交状态设为false
    isSubmitting.value = false
  }
}

// 使用防抖包装提交函数，防止频繁点击
const debouncedSubmitAllProgress = debounce(handleSubmitAllProgress, 500)

// 清空已安排场地 - 显示确认对话框
const handleClearArrangedVenue = (item: TeachingScheduleItemExtended) => {
  currentClearItem.value = item
  confirmDialogRef.value?.show()
}

// 确认清空场地
const handleConfirmClearVenue = () => {
  if (currentClearItem.value) {
    // 清空场地相关信息
    currentClearItem.value.skcdmc = ''
    // 可以根据需要清空其他相关字段
    console.log('清空已安排场地', currentClearItem.value)
    toast.show({ msg: '已清空场地安排', iconName: 'success' })
    currentClearItem.value = null
  }
}

// 取消清空场地
const handleCancelClearVenue = () => {
  currentClearItem.value = null
}
</script>

<template>
  <view class="container">
    <!-- 课程基础信息卡片 -->
    <view class="course-info-card">
      <view class="info-section">
        <view class="info-item">
          <text class="info-label">班级</text>
          <text class="info-value">{{ courseInfo.className }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">课程</text>
          <text class="info-value">
            {{ courseInfo.courseName }}
            <text class="period-text">{{ courseInfo.coursePeriod }}</text>
          </text>
        </view>
      </view>

      <view class="divider"></view>

      <view class="info-section">
        <view class="hours-info">
          <text class="hours-item">
            本学期学时：
            <text class="highlight-text">{{ courseInfo.totalHours }}</text>
          </text>
          <text class="hours-item">
            周学时：
            <text class="highlight-text">{{ courseInfo.weeklyHours }}/周</text>
          </text>
          <text class="hours-item">
            周数：
            <text class="highlight-text">{{ courseInfo.currentWeek }}周</text>
          </text>
        </view>
      </view>

      <view class="divider"></view>

      <view class="info-section">
        <view class="info-item">
          <text class="info-label">课代表</text>
          <text class="info-value">
            {{ courseInfo.representative.id }} {{ courseInfo.representative.name }}
          </text>
        </view>
      </view>
    </view>

    <!-- 授课进度内容 -->
    <view class="content">
      <view class="title-row">
        <text class="title">授课进度制定</text>
        <view class="actions">
          <!-- <view class="action-btn add-btn" @click="handleAddProgress">
            <wd-icon name="add" size="28rpx" />
            <text>添加</text>
          </view> -->
        </view>
      </view>

      <!-- 数据占位符 -->
      <view v-if="!hasData" class="placeholder">
        <wd-icon name="note" size="80rpx" color="#cccccc" />
        <text class="placeholder-text">暂无授课进度数据</text>
        <!-- <view class="action-btn add-btn" style="margin-top: 24rpx" @click="handleAddProgress">
          <wd-icon name="add" size="28rpx" />
          <text>添加授课进度</text>
        </view> -->
      </view>

      <!-- 授课进度卡片列表 -->
      <view v-else class="progress-list">
        <view v-for="(item, index) in progressList" :key="item.id" class="progress-card">
          <!-- 基本信息部分 -->
          <view class="progress-header">
            <view class="progress-id">{{ index + 1 }}</view>
            <view class="progress-basic-info">
              <text class="info-text date">{{ item.skrq }}</text>
              <text class="info-text">第{{ item.zc }}周</text>
              <text class="info-text">{{ formatWeekDay(item.xqs) }}</text>
              <text class="info-text">第{{ item.jc || item.jcshow }}节</text>
            </view>
          </view>

          <!-- 授课详情 -->
          <view class="progress-details">
            <view class="detail-row full-width">
              <view class="detail-item">
                <text class="detail-label">授课教师</text>
                <picker
                  :value="getTeacherIndex(item)"
                  :range="teacherNameList"
                  @change="(e) => handleTeacherChange(e, item)"
                  class="teacher-picker"
                >
                  <view class="picker-value">
                    {{ item.skjsxm || item.skjs || '请选择教师' }}
                    <wd-icon name="arrow-down" size="24rpx" color="#999999" />
                  </view>
                </picker>
              </view>
            </view>

            <view class="detail-row full-width">
              <view class="detail-item">
                <text class="detail-label">授课方式</text>
                <picker
                  :value="getTeachingMethodIndex(item)"
                  :range="teachingMethodNameList"
                  @change="(e) => handleTeachingMethodChange(e, item)"
                  class="teaching-method-picker"
                >
                  <view class="picker-value">
                    {{ item.skfsmc || item.skfs || '请选择授课方式' }}
                    <wd-icon name="arrow-down" size="24rpx" color="#999999" />
                  </view>
                </picker>
              </view>
            </view>

            <view class="detail-row full-width">
              <view class="detail-item">
                <text class="detail-label">教室要求</text>
                <picker
                  v-if="item.skjhgzlrdzt === 0"
                  :value="getSiteTypeIndex(item)"
                  :range="siteTypeNameList"
                  @change="(e) => handleSiteTypeChange(e, item)"
                  class="site-type-picker"
                >
                  <view class="picker-value">
                    {{ item.skcdlxshow || '请选择教室要求' }}
                    <wd-icon name="arrow-down" size="24rpx" color="#999999" />
                  </view>
                </picker>
              </view>
            </view>
            <view class="detail-row full-width" v-if="item.skcdmc">
              <view class="detail-item">
                <text class="detail-label"></text>
                <view class="detail-value">{{ item.skcdmc }}</view>
              </view>
              <!-- 清空已安排场地按钮 -->
              <view class="detail-item mt-2">
                <text class="detail-label"></text>
                <ActionButton
                  type="danger"
                  text="清空已安排场地"
                  @click="handleClearArrangedVenue(item)"
                />
              </view>
            </view>

            <view class="detail-row full-width">
              <view class="detail-item">
                <text class="detail-label">分组</text>
                <picker
                  :value="getStudentGroupIndex(item)"
                  :range="studentGroupNameList"
                  @change="(e) => handleStudentGroupChange(e, item)"
                  class="student-group-picker"
                >
                  <view class="picker-value">
                    {{ item.xsfzmc || item.xsfzid || '' }}
                    <wd-icon name="arrow-down" size="24rpx" color="#999999" />
                  </view>
                </picker>
              </view>
            </view>

            <view class="detail-row full-width">
              <view class="detail-item flex flex-col items-start w-full">
                <text class="detail-label w-full mb-3">授课内容</text>
                <textarea
                  v-model="item.sknl"
                  class="detail-textarea box-border"
                  placeholder="请输入授课内容"
                />
              </view>
            </view>

            <!-- 教案附件上传组件 -->
            <view class="detail-row full-width">
              <view class="detail-item file-uploader-container">
                <text class="detail-label">教案附件</text>
                <FileUploader
                  v-model="item.lessonAttachments"
                  uploadType="teaching_lesson"
                  title=""
                  :showTitle="false"
                  tipText="支持上传教案相关附件"
                  @update:modelValue="(files) => handleLessonAttachmentsChange(files, item)"
                  class="custom-file-uploader"
                />
              </view>
            </view>
          </view>

          <!-- 作业信息 -->
          <view class="progress-section">
            <view class="section-header">
              <wd-icon name="edit-outline" size="32rpx" color="#3370ff" />
              <text class="section-title">作业</text>
            </view>
            <view class="section-content">
              <view class="section-row">
                <view class="section-item">
                  <text class="section-label">题数</text>
                  <input
                    v-model="item.zyts"
                    type="number"
                    class="section-input bg-white"
                    placeholder="请输入题数"
                  />
                </view>
              </view>

              <view class="section-row full-width">
                <view class="section-item">
                  <text class="section-label">批改方式</text>
                  <picker
                    :value="getHomeworkCorrectionIndex(item)"
                    :range="homeworkCorrectionNameList"
                    @change="(e) => handleHomeworkCorrectionChange(e, item)"
                    class="homework-correction-picker"
                  >
                    <view class="picker-value bg-white">
                      {{ item.zypgfsmc || item.zypgfs || '请选择批改方式' }}
                      <wd-icon name="arrow-down" size="24rpx" color="#999999" />
                    </view>
                  </picker>
                </view>
              </view>

              <view class="section-row full-width">
                <view class="section-item flex flex-col items-start w-full">
                  <text class="section-label w-full mb-3">内容</text>
                  <textarea
                    v-model="item.zynr"
                    class="section-textarea box-border"
                    placeholder="请输入作业内容"
                  />
                </view>
              </view>

              <!-- 作业附件上传组件 -->
              <view class="section-row full-width">
                <view class="section-item file-uploader-container">
                  <text class="section-label">附件</text>
                  <FileUploader
                    v-model="item.homeworkAttachments"
                    uploadType="teaching_homework"
                    title=""
                    :showTitle="false"
                    tipText="支持上传作业相关附件"
                    @update:modelValue="(files) => handleHomeworkAttachmentsChange(files, item)"
                    class="custom-file-uploader"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 实践信息 -->
          <view class="progress-section">
            <view class="section-header">
              <wd-icon name="computer" size="32rpx" color="#3370ff" />
              <text class="section-title">实践</text>
            </view>
            <view class="section-content">
              <view class="section-row">
                <view class="section-item">
                  <text class="section-label">人数/组数</text>
                  <input
                    v-model="item.fzrszs"
                    class="section-input bg-white"
                    placeholder="请输入人数或组数"
                  />
                </view>
              </view>

              <view class="section-row full-width">
                <view class="section-item flex flex-col items-start w-full">
                  <text class="section-label w-full mb-3">每组使用仪器设备及数量</text>
                  <textarea
                    v-model="item.syyqsbsl"
                    class="section-textarea box-border"
                    placeholder="请输入仪器设备及数量"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 全局保存按钮 -->
    <view class="fixed-bottom-actions">
      <view
        class="global-action save-btn"
        :class="{ 'disabled-btn': isSaving }"
        @click="debouncedSaveAllProgress"
      >
        <wd-icon v-if="!isSaving" name="check" size="32rpx" color="#ffffff" />
        <wd-icon v-else name="refresh" size="32rpx" color="#ffffff" class="loading-icon" />
        <text>{{ isSaving ? '保存中...' : '进度信息保存' }}</text>
      </view>
      <view
        class="global-action submit-btn"
        :class="{ 'disabled-btn': isSubmitting }"
        @click="debouncedSubmitAllProgress"
      >
        <wd-icon v-if="!isSubmitting" name="check-circle-filled" size="32rpx" color="#ffffff" />
        <wd-icon v-else name="refresh" size="32rpx" color="#ffffff" class="loading-icon" />
        <text>{{ isSubmitting ? '提交中...' : '提交授课计划' }}</text>
      </view>
    </view>

    <!-- 确认清空场地对话框 -->
    <ConfirmDialog
      ref="confirmDialogRef"
      title="清空场地安排"
      confirm-text="确认清空"
      cancel-text="取消"
      @confirm="handleConfirmClearVenue"
      @cancel="handleCancelClearVenue"
    >
      <template #content>
        <view v-if="currentClearItem" class="clear-venue-content">
          <view class="content-section">
            <text class="section-title">授课教师</text>
            <text class="section-value">
              {{ currentClearItem.skjsxm || currentClearItem.skjs || '未设置' }}
            </text>
          </view>

          <view class="content-section">
            <text class="section-title">班级课程</text>
            <text class="section-value">
              总学时：{{ courseInfo.totalHours }} 周学时：{{ courseInfo.weeklyHours }}/周 周数：{{
                courseInfo.currentWeek
              }}周
            </text>
            <text class="section-value">{{ courseInfo.className }}</text>
          </view>

          <view class="content-section">
            <text class="section-title">日期节次</text>
            <text class="section-value">
              {{ currentClearItem.skrq }} 第{{ currentClearItem.zc }}周
              {{ formatWeekDay(currentClearItem.xqs) }} 第{{
                currentClearItem.jc || currentClearItem.jcshow
              }}节
            </text>
          </view>

          <view class="content-section">
            <text class="section-title">授课内容</text>
            <text class="section-value">{{ currentClearItem.sknl || '未设置' }}</text>
          </view>

          <view class="content-section">
            <text class="section-title">授课场地</text>
            <text class="section-value highlight">{{ currentClearItem.skcdmc }}</text>
          </view>
        </view>
      </template>
    </ConfirmDialog>
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  min-height: 99vh;
  padding: 24rpx;
  padding-bottom: 150rpx; /* 为底部固定按钮留出空间 */
  background-color: #f5f5f7;
}

.course-info-card {
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-section {
  padding: 24rpx 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  min-width: 100rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.period-text {
  margin-left: 8rpx;
  font-size: 26rpx;
  font-weight: normal;
  color: #666666;
}

.divider {
  width: 100%;
  height: 1rpx;
  background-color: #f0f0f0;
}

.hours-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.hours-item {
  font-size: 28rpx;
  color: #666666;
}

.highlight-text {
  font-weight: 500;
  color: #333333;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.actions {
  display: flex;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #3370ff;
  border-radius: 8rpx;
}

.action-btn text {
  margin-left: 4rpx;
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  padding: 48rpx 0;
}

.placeholder-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #8e8e93;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.progress-card {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.progress-header {
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.progress-id {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  background-color: #3370ff;
  border-radius: 28rpx;
}

.progress-basic-info {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  gap: 8rpx;
  align-items: center;
  margin-left: 24rpx;
}

.info-text {
  padding: 4rpx 16rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #f5f7fa;
  border-radius: 6rpx;
}

.card-actions {
  display: flex;
  align-items: center;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-bottom: 16rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row {
  display: flex;
  gap: 32rpx;
}

.detail-row.full-width {
  flex-direction: column;
}

.detail-item {
  display: flex;
  flex: 1;
  align-items: flex-start;
}

.detail-label {
  min-width: 140rpx;
  font-size: 28rpx;
  color: #666666;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}
/* Picker相关样式 */
.teacher-picker,
.teaching-method-picker,
.site-type-picker,
.student-group-picker,
.homework-correction-picker {
  flex: 1;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6rpx 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #f5f7fa;
  border-radius: 6rpx;
}
.picker-value.bg-white {
  background-color: #ffffff !important;
}

.progress-section {
  display: flex;
  flex-direction: column;
  padding: 16rpx;
  margin-bottom: 16rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 12rpx;
  margin-bottom: 16rpx;
  border-bottom: 1rpx dashed #e0e0e0;
}

.section-title {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.section-row {
  display: flex;
  gap: 32rpx;
}

.section-row.full-width {
  flex-direction: column;
}

.section-item {
  display: flex;
  flex: 1;
  align-items: flex-start;
}

.section-label {
  min-width: 220rpx;
  font-size: 26rpx;
  color: #666666;
}

.section-value {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  word-break: break-all;
}

.section-input {
  padding: 6rpx 12rpx;
  font-size: 26rpx;
  color: #333333;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
}

.section-textarea {
  width: 100%;
  height: 120rpx;
  padding: 12rpx;
  font-size: 26rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 8rpx;
}

.attachment-item {
  display: inline-flex;
  gap: 8rpx;
  align-items: center;
  padding: 8rpx 16rpx;
  cursor: pointer;
  background-color: rgba(51, 112, 255, 0.08);
  border-radius: 6rpx;
}

.attachment-name {
  font-size: 26rpx;
  color: #3370ff;
}

.info-text.date {
  color: #3370ff;
  background-color: rgba(51, 112, 255, 0.1);
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.edit-action {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #ffffff;
  border-radius: 8rpx;
}

.edit-action.save {
  background-color: #52c41a;
}

.edit-action.cancel {
  background-color: #8c8c8c;
}

.edit-action text {
  margin-left: 4rpx;
}

.global-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
  padding: 24rpx;
  margin-top: 24rpx;
}

.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  gap: 24rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 24rpx 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.global-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40%;
  padding: 24rpx 16rpx;
  font-size: 32rpx;
  color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.global-action.save-btn {
  background-color: #52c41a;
}

.global-action.submit-btn {
  background-color: #1890ff;
}

.global-action:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.98);
}

.global-action text {
  margin-left: 8rpx;
}
/* 文件上传组件容器样式 */
.file-uploader-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}

.file-uploader-container .detail-label,
.file-uploader-container .section-label {
  margin-bottom: 8rpx;
}
/* 覆盖FileUploader组件样式 */
.custom-file-uploader {
  /* 自定义文件上传组件样式 */
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.custom-file-uploader .upload-buttons {
  flex-wrap: wrap;
  justify-content: flex-start;
  max-width: 100%;
}

.custom-file-uploader .upload-button {
  width: 160rpx;
  height: 56rpx;
  margin-bottom: 8rpx;
  font-size: 22rpx;
}

.custom-file-uploader .upload-tip {
  width: 100%;
  overflow: hidden;
  font-size: 22rpx;
  color: #999;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.custom-file-uploader .attachment-list {
  width: 100%;
}

.custom-file-uploader .attachment-item {
  padding: 8rpx;
  margin-bottom: 8rpx;
}

.custom-file-uploader .attachment-name {
  max-width: 160rpx;
  font-size: 24rpx;
}

.custom-file-uploader .attachment-action {
  width: 48rpx;
  height: 48rpx;
}

.detail-input {
  flex: 1;
  padding: 8rpx 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #f5f7fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
}

.detail-textarea {
  width: 100%;
  height: 120rpx;
  padding: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
}

.global-action.disabled-btn {
  pointer-events: none;
  background-color: #999999;
  opacity: 0.7;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  animation: rotating 1.5s linear infinite;
}
/* 清空场地对话框内容样式 */
.clear-venue-content {
  padding: 16rpx 0;
  text-align: left;
}

.content-section {
  margin-bottom: 24rpx;
}

.content-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.section-value {
  display: block;
  margin-bottom: 4rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #666666;
}

.section-value:last-child {
  margin-bottom: 0;
}

.section-value.highlight {
  font-weight: 500;
  color: #ff4d4f;
}
</style>
